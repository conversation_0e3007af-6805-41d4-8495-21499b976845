import tkinter as tk
from tkinter import messagebox

class Calculator:
    def __init__(self, master):
        self.master = master
        master.title("Python计算器")
        master.geometry("300x400")
        master.resizable(False, False)
        
        # 显示结果的文本框
        self.result_var = tk.StringVar()
        self.result_var.set("0")
        self.result = tk.Entry(master, textvariable=self.result_var, font=('Arial', 20), bd=10, 
                              insertwidth=4, width=14, justify='right')
        self.result.grid(row=0, column=0, columnspan=4, pady=10)
        
        # 按钮布局
        button_list = [
            '7', '8', '9', '/',
            '4', '5', '6', '*',
            '1', '2', '3', '-',
            '0', '.', '=', '+'
        ]
        
        # 创建按钮
        r = 1
        c = 0
        for button_text in button_list:
            cmd = lambda x=button_text: self.click(x)
            tk.Button(master, text=button_text, width=5, height=2, font=('Arial', 15),
                     command=cmd).grid(row=r, column=c, padx=5, pady=5)
            c += 1
            if c > 3:
                c = 0
                r += 1
        
        # 添加清除按钮
        tk.Button(master, text="C", width=5, height=2, font=('Arial', 15),
                 command=self.clear).grid(row=r, column=c, padx=5, pady=5)
        
    def click(self, key):
        if key == "=":
            # 计算结果
            try:
                result = eval(self.result_var.get())
                self.result_var.set(str(result))
            except Exception as e:
                messagebox.showerror("错误", "计算错误: " + str(e))
                self.result_var.set("0")
        else:
            # 如果当前显示为0，则替换；否则追加
            if self.result_var.get() == "0":
                self.result_var.set(key)
            else:
                self.result_var.set(self.result_var.get() + key)
    
    def clear(self):
        # 清除显示
        self.result_var.set("0")

# 创建主窗口并运行
if __name__ == "__main__":
    root = tk.Tk()
    calculator = Calculator(root)
    root.mainloop()